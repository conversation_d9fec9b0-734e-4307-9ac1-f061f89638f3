<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Primary app colors for dark mode -->
    <color name="colorPrimary">#374151</color>
    <color name="colorPrimaryDark">#111827</color>
    <color name="colorAccent">#374151</color>
    
    <!-- Dark theme safe area colors (gray-450 - exactly half intensity of header gray-900) -->
    <color name="statusBarColor">#8F9BB3</color>        <!-- gray-450 (half of gray-900) -->
    <color name="navigationBarColor">#8F9BB3</color>    <!-- gray-450 (half of gray-900) -->

    <!-- Theme-specific safe area colors for dark mode -->
    <!-- All themes use half-intensity gray in dark mode for consistency -->
    <color name="statusBarColorElectric">#8F9BB3</color>        <!-- gray-450 (half of gray-900 #111827) -->
    <color name="navigationBarColorElectric">#8F9BB3</color>    <!-- gray-450 (half of gray-900 #111827) -->

    <color name="statusBarColorGreen">#8F9BB3</color>           <!-- gray-450 (half of gray-900 #111827) -->
    <color name="navigationBarColorGreen">#8F9BB3</color>       <!-- gray-450 (half of gray-900 #111827) -->

    <color name="statusBarColorTeal">#8F9BB3</color>            <!-- gray-450 (half of gray-900 #111827) -->
    <color name="navigationBarColorTeal">#8F9BB3</color>        <!-- gray-450 (half of gray-900 #111827) -->

    <color name="statusBarColorPink">#8F9BB3</color>            <!-- gray-450 (half of gray-900 #111827) -->
    <color name="navigationBarColorPink">#8F9BB3</color>        <!-- gray-450 (half of gray-900 #111827) -->
</resources>
