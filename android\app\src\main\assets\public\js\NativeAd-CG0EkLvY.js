import{a as E,C as g,j as i}from"../assets/index-Bb6RUuxY.js";import{r as c}from"./vendor-C67cHu0f.js";const w={NATIVE_AD:"ca-app-pub-5519957327506488/XXXXXXXXXX",BANNER_AD:"ca-app-pub-5519957327506488/XXXXXXXXXX",INTERSTITIAL_AD:"ca-app-pub-5519957327506488/XXXXXXXXXX",REWARDED_AD:"ca-app-pub-5519957327506488/XXXXXXXXXX"},p=w,u={NATIVE_AD_UNIT_ID:p.NATIVE_AD,BANNER_AD_UNIT_ID:p.BANNER_AD,INTERSTITIAL_AD_UNIT_ID:p.INTERSTITIAL_AD,REWARDED_AD_UNIT_ID:p.REWARDED_AD,TEXT_LIMITS:{HEADLINE:25,BODY:90,CTA:15,ADVERTISER:25}},C=s=>{switch(s.toLowerCase()){case"native":return u.NATIVE_AD_UNIT_ID;case"banner":return u.BANNER_AD_UNIT_ID;case"interstitial":return u.INTERSTITIAL_AD_UNIT_ID;case"rewarded":return u.REWARDED_AD_UNIT_ID;default:throw new Error(`Unknown ad type: ${s}`)}},h=(s,o)=>{const r=u.TEXT_LIMITS[o.toUpperCase()];return r&&s&&s.length>r?`${s.substring(0,r)}...`:s},U=({className:s=""})=>{const{theme:o}=E(),[r,N]=c.useState([]),[v,m]=c.useState(!1),[f,d]=c.useState(null),[x,l]=c.useState("idle"),I=c.useRef(null),b=C("native"),A=()=>{try{const{AdMobNative:e}=g.Plugins;if(e)return e;const{AdmobAds:a}=g.Plugins;if(!a)throw new Error("AdmobAds plugin not found. Make sure capacitor-admob-ads is installed and synced.");return a}catch(e){throw e}},D=async()=>{m(!0),d(null),l("loading");try{const e=A(),a=e.constructor.name==="AdMobNative"||e.loadNativeAd.toString().includes("adUnitId");let t;a?(console.log("Using custom AdMobNative plugin for proper validator support"),t=await e.loadNativeAd({adUnitId:b}),t&&t.success&&t.adData&&(t={ads:[{id:"native-ad-1",headline:t.adData.headline,body:t.adData.body,cta:t.adData.callToAction,icon:t.adData.icon,cover:null,advertiser:t.adData.advertiser,adChoicesUrl:null}]})):(console.log("Using capacitor-admob-ads plugin (limited validator support)"),t=await e.loadNativeAd({adId:b,isTesting:!0,adsCount:1,nativeAdOptions:{adChoicesPlacement:"TOP_RIGHT",mediaAspectRatio:"LANDSCAPE",requestMultipleImages:!1}})),console.log("AdMob Load Result:",JSON.stringify(t,null,2)),t&&t.ads&&t.ads.length>0?(N(t.ads),l("loaded"),d(null),t.ads.forEach((n,X)=>{console.log(`Ad ${X} Structure:`,{hasHeadline:!!n.headline,hasBody:!!n.body,hasCta:!!n.cta,hasIcon:!!n.icon,hasCover:!!n.cover,hasAdvertiser:!!n.advertiser,hasAdChoicesUrl:!!n.adChoicesUrl,headlineLength:n.headline?.length||0,bodyLength:n.body?.length||0,ctaLength:n.cta?.length||0})})):t&&t.success===!1?(l("error"),d("Failed to load ad: "+(t.error||"Unknown error"))):(l("no-ads"),d("No advertisements available at this time. Result: "+JSON.stringify(t)))}catch(e){const a=e.message||e.toString();console.error("AdMob Load Error:",e),d("Failed to load advertisement: "+a),l("error")}finally{m(!1)}},y=async e=>{try{await A().triggerNativeAd({id:e})}catch(a){typeof window<"u"&&alert(`Ad Click Error: ${a.message}`)}},T=async()=>{try{const e=A();if((e.constructor.name==="AdMobNative"||e.initialize)&&e.initialize){console.log("Initializing custom AdMobNative plugin...");const t=await e.initialize();if(console.log("AdMob initialization result:",t),!t.success)throw new Error(t.error||"Failed to initialize AdMob")}await D()}catch(e){d("Failed to load AdMob: "+e.message),l("error")}};c.useEffect(()=>{setTimeout(T,1e3)},[]);const _=g.getPlatform()==="web";return x==="error"||x==="no-ads"?i.jsx("div",{className:`native-ad-container ${s}`,children:i.jsx("div",{className:"text-center py-4 text-gray-500 dark:text-gray-400 text-sm",children:f||"Advertisement unavailable"})}):v&&r.length===0?i.jsx("div",{className:`native-ad-container ${s}`,children:i.jsxs("div",{className:"native-ad-loading",children:[i.jsx("div",{className:"loading-spinner"}),i.jsx("p",{className:"loading-text",children:"Loading advertisement..."})]})}):i.jsxs("div",{className:`native-ad-container ${s}`,children:[_&&i.jsx("div",{className:`mb-2 px-3 py-1 rounded-full text-xs ${o.background} border ${o.border} inline-block`,children:"🔧 Web Testing Mode (Mock Ads)"}),r.length>0&&r.map((e,a)=>i.jsxs("div",{ref:I,className:`relative rounded-lg ${o.card} border ${o.border} overflow-hidden cursor-pointer`,style:{minWidth:"120px",minHeight:"120px",width:"auto",height:"auto"},onClick:()=>y(e.id),children:[i.jsx("div",{className:"ad-attribution-badge",style:{position:"absolute",top:"4px",left:"4px",backgroundColor:"rgba(0, 0, 0, 0.9)",color:"white",fontSize:"10px",fontWeight:"700",padding:"2px 6px",borderRadius:"2px",zIndex:30,minWidth:"15px",minHeight:"15px",textTransform:"uppercase",letterSpacing:"0.5px",fontFamily:"Arial, sans-serif"},children:"AD"}),i.jsx("div",{className:"ad-choices-overlay",style:{position:"absolute",top:"4px",right:"4px",backgroundColor:"rgba(255, 255, 255, 0.95)",border:"1px solid #ddd",fontSize:"12px",fontWeight:"500",padding:"4px 6px",borderRadius:"3px",zIndex:30,minWidth:"15px",minHeight:"15px",color:"#666",cursor:"pointer",fontFamily:"Arial, sans-serif"},onClick:t=>{t.stopPropagation(),e.adChoicesUrl&&window.open(e.adChoicesUrl,"_blank")},children:"ⓘ"}),i.jsxs("div",{className:"ad-content-clickable p-4",style:{paddingTop:"40px"},children:[i.jsxs("div",{className:"flex items-start space-x-3 mb-3",children:[e.icon&&i.jsx("img",{src:e.icon,alt:"Ad Icon",className:"w-12 h-12 rounded-lg flex-shrink-0",style:{aspectRatio:"1:1"},crossOrigin:"anonymous",onError:t=>{t.target.style.display="none"}}),i.jsxs("div",{className:"flex-1 min-w-0",children:[i.jsx("h3",{className:`text-sm font-semibold ${o.text} mb-1`,style:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",maxWidth:"100%"},title:e.headline||"Sponsored Content",children:h(e.headline||"Sponsored Content","headline")}),e.advertiser&&i.jsx("p",{className:`text-xs ${o.textSecondary}`,children:h(e.advertiser,"advertiser")})]})]}),e.body&&i.jsx("p",{className:`text-sm ${o.text} mb-3`,style:{lineHeight:"1.4"},children:h(e.body,"body")}),e.cover&&i.jsx("div",{className:"rounded-lg overflow-hidden mb-3",children:i.jsx("img",{src:e.cover,alt:"Ad Media",className:"w-full h-32 object-cover",crossOrigin:"anonymous",onError:t=>{t.target.style.display="none"}})}),i.jsx("button",{className:`w-full py-3 px-4 ${o.primary} text-white rounded-lg text-sm font-semibold transition-colors hover:opacity-90`,style:{minHeight:"40px"},children:h(e.cta||"Learn More","cta")})]}),e.adChoicesUrl&&i.jsx("div",{className:"ad-choices",children:i.jsx("button",{className:"ad-choices-button",onClick:t=>{t.stopPropagation(),window.open(e.adChoicesUrl,"_blank")},children:"ⓘ"})})]},e.id||a))]})};export{U as N};
