<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Primary app colors -->
    <color name="colorPrimary">#60A5FA</color>
    <color name="colorPrimaryDark">#3B82F6</color>
    <color name="colorAccent">#60A5FA</color>

    <!-- Light theme safe area colors (500 shades - 85% intensity of header 550 shades) -->
    <!-- Electric Blue theme -->
    <color name="statusBarColor">#3B82F6</color>        <!-- blue-500 (85% of blue-550) -->
    <color name="navigationBarColor">#3B82F6</color>    <!-- blue-500 (85% of blue-550) -->

    <!-- Theme-specific safe area colors -->
    <!-- Electric Blue (default) -->
    <color name="statusBarColorElectric">#8DC5F7</color>        <!-- blue-275 (half of blue-550 #1a56db) -->
    <color name="navigationBarColorElectric">#8DC5F7</color>    <!-- blue-275 (half of blue-550 #1a56db) -->

    <!-- <PERSON> Green -->
    <color name="statusBarColorGreen">#87CFB7</color>           <!-- green-275 (half of green-550 #0e9f6e) -->
    <color name="navigationBarColorGreen">#87CFB7</color>       <!-- green-275 (half of green-550 #0e9f6e) -->

    <!-- Ocean Teal -->
    <color name="statusBarColorTeal">#86EAC4</color>            <!-- teal-275 (half of teal-550 #0d9488) -->
    <color name="navigationBarColorTeal">#86EAC4</color>        <!-- teal-275 (half of teal-550 #0d9488) -->

    <!-- Sunset Pink -->
    <color name="statusBarColorPink">#ED93BB</color>            <!-- pink-275 (half of pink-550 #db2777) -->
    <color name="navigationBarColorPink">#ED93BB</color>        <!-- pink-275 (half of pink-550 #db2777) -->
</resources>
