# Problems and Solutions

## Purpose
This file tracks recurring mistakes and their corresponding solutions to prevent repeating the same errors. **ALWAYS refer to this file before implementing changes or investigating problems.**

## Template for New Entries
```
### Problem: [Brief Description]
**Date:** [YYYY-MM-DD]
**Context:** [What was being worked on]
**Issue:** [Detailed description of the problem]
**Root Cause:** [Why it happened]
**Solution:** [How it was fixed]
**Prevention:** [How to avoid this in the future]
**Related Files:** [List of affected files]

---
```

## Documented Problems and Solutions

### Problem: Modified Android Styles Beyond Safe Areas
**Date:** 2025-01-20
**Context:** Implementing theme-aware safe area colors for Android status bar and navigation bar
**Issue:** Added windowLightStatusBar and windowLightNavigationBar settings to styles.xml, changing app-wide behavior instead of only affecting safe area colors
**Root Cause:** Assumed that Android styles needed to be modified to support safe area theming, when the existing JavaScript hook already handled this dynamically
**Solution:** Reverted styles.xml to original state, kept only color changes in colors.xml files
**Prevention:** 
- Always check original file state with `git show HEAD:path/to/file` before making changes
- Verify what the user actually requested vs. what seems "necessary"
- Ask user to confirm scope of changes before implementing
**Related Files:** 
- `android/app/src/main/res/values/styles.xml`
- `android/app/src/main/res/values-night/styles.xml` (incorrectly created)

---

### Problem: Scope Creep in Implementation
**Date:** 2025-01-20
**Context:** User requested safe area color changes only
**Issue:** Made additional behavioral changes beyond the requested scope
**Root Cause:** Assumed additional changes were needed without confirming with user
**Solution:** Stick strictly to what was requested, ask before making any additional changes
**Prevention:** 
- Re-read user request carefully before starting
- List exactly what will be changed before implementing
- Ask "Should I also..." for any additional changes that seem related
**Related Files:** Various

---

### Problem: Navigation Bar Plugin Method Name Mismatch
**Date:** 2025-01-20
**Context:** Implementing navigation bar color theming to match status bar
**Issue:** JavaScript code was calling `NavigationBar.setColor()` but the actual plugin method is `setNavigationBarColor()`, causing navigation bar colors to not apply
**Root Cause:** Incorrect assumption about plugin API method names without checking the actual plugin documentation
**Solution:**
- Checked the plugin source code in `node_modules/@capgo/capacitor-navigation-bar/android/src/main/java/ee/forgr/capacitor_navigation_bar/NavigationBarPlugin.java`
- Found the correct method name is `setNavigationBarColor()`
- Updated JavaScript code to use the correct method name
**Prevention:**
- Always check plugin documentation or source code for correct API method names
- Test plugin methods individually before implementing complex logic
- Use browser dev tools to check for JavaScript errors when plugins don't work as expected
**Related Files:**
- `src/hooks/useStatusBar.js`
- `node_modules/@capgo/capacitor-navigation-bar/README.md`

---

### Problem: Status Bar Text Invisible in Dark Themes
**Date:** 2025-01-20
**Context:** User reported status bar content not visible in Rose Pink and Dark themes
**Issue:** Status bar was using `DARK` style (dark text) for all themes, making text invisible on dark backgrounds in Rose Pink and Dark themes
**Root Cause:** Oversimplified status bar style logic that didn't account for dark theme backgrounds
**Solution:**
- Enhanced status bar style logic to detect dark themes: `(currentTheme === 'dark' || currentTheme === 'pink')`
- Dark themes now use `LIGHT` style (white text/icons)
- Light themes continue using `DARK` style (dark text/icons)
- Updated navigation bar button logic to match
**Prevention:**
- Always test status bar visibility in all available themes
- Consider background color intensity when choosing text/icon colors
- Test on actual device as simulator may not accurately show status bar visibility
**Related Files:**
- `src/hooks/useStatusBar.js`

---

### Problem: Safe Area Color Intensity Too Light
**Date:** 2025-01-20
**Context:** User requested darker safe area colors for better visual separation
**Issue:** Safe areas were using half-intensity colors (275 shades) which appeared too light compared to header/footer
**Root Cause:** Initial implementation used conservative 50% intensity without considering visual impact
**Solution:**
- Upgraded from 50% intensity (275 shades) to 85% intensity (500 shades)
- Updated all theme colors: Electric (#3B82F6), Green (#10B981), Teal (#14B8A6), Pink (#EC4899), Dark (#4B5563)
- Applied changes to JavaScript hook, CSS classes, and Android colors.xml
**Prevention:**
- Start with higher intensity colors for better visual contrast
- Always ask user for feedback on color intensity before finalizing
- Test color combinations on actual device for accurate appearance
**Related Files:**
- `src/hooks/useStatusBar.js`
- `src/index.css`
- `android/app/src/main/res/values/colors.xml`

---

### Problem: Navigation Bar Plugin Method Name Mismatch
**Date:** 2025-01-20
**Context:** Implementing navigation bar color theming to match status bar
**Issue:** JavaScript code was calling `NavigationBar.setColor()` but the actual plugin method is `setNavigationBarColor()`, causing navigation bar colors to not apply
**Root Cause:** Incorrect assumption about plugin API method names without checking the actual plugin documentation
**Solution:**
- Checked the plugin source code in `node_modules/@capgo/capacitor-navigation-bar/android/src/main/java/ee/forgr/capacitor_navigation_bar/NavigationBarPlugin.java`
- Found the correct method name is `setNavigationBarColor()`
- Updated JavaScript code to use the correct method name
**Prevention:**
- Always check plugin documentation or source code for correct API method names
- Test plugin methods individually before implementing complex logic
- Use browser dev tools to check for JavaScript errors when plugins don't work as expected
**Related Files:**
- `src/hooks/useStatusBar.js`
- `node_modules/@capgo/capacitor-navigation-bar/README.md`

---

### Problem: Status Bar Text Invisible in Dark Themes
**Date:** 2025-01-20
**Context:** User reported status bar content not visible in Rose Pink and Dark themes
**Issue:** Status bar was using `DARK` style (dark text) for all themes, making text invisible on dark backgrounds in Rose Pink and Dark themes
**Root Cause:** Oversimplified status bar style logic that didn't account for dark theme backgrounds
**Solution:**
- Enhanced status bar style logic to detect dark themes: `(currentTheme === 'dark' || currentTheme === 'pink')`
- Dark themes now use `LIGHT` style (white text/icons)
- Light themes continue using `DARK` style (dark text/icons)
- Updated navigation bar button logic to match
**Prevention:**
- Always test status bar visibility in all available themes
- Consider background color intensity when choosing text/icon colors
- Test on actual device as simulator may not accurately show status bar visibility
**Related Files:**
- `src/hooks/useStatusBar.js`

---

### Problem: Safe Area Color Intensity Too Light
**Date:** 2025-01-20
**Context:** User requested darker safe area colors for better visual separation
**Issue:** Safe areas were using half-intensity colors (275 shades) which appeared too light compared to header/footer
**Root Cause:** Initial implementation used conservative 50% intensity without considering visual impact
**Solution:**
- Upgraded from 50% intensity (275 shades) to 85% intensity (500 shades)
- Updated all theme colors: Electric (#3B82F6), Green (#10B981), Teal (#14B8A6), Pink (#EC4899), Dark (#4B5563)
- Applied changes to JavaScript hook, CSS classes, and Android colors.xml
**Prevention:**
- Start with higher intensity colors for better visual contrast
- Always ask user for feedback on color intensity before finalizing
- Test color combinations on actual device for accurate appearance
**Related Files:**
- `src/hooks/useStatusBar.js`
- `src/index.css`
- `android/app/src/main/res/values/colors.xml`

---

## Guidelines for Using This File

1. **Before Starting Any Work:**
   - Read through this entire file
   - Check if similar problems have been encountered
   - Apply documented prevention strategies

2. **When Encountering Issues:**
   - Check if the problem matches any documented patterns
   - Apply known solutions first
   - If new problem, document it using the template above

3. **After Solving Problems:**
   - Always add new problems and solutions to this file
   - Include enough detail for future reference
   - Update prevention strategies based on lessons learned

4. **Regular Maintenance:**
   - Review this file periodically
   - Update solutions if better approaches are found
   - Remove outdated entries if no longer relevant

## Common Mistake Patterns to Watch For

- **Scope Creep:** Making changes beyond what was explicitly requested
- **Assumption-Based Changes:** Modifying files without understanding original purpose
- **Incomplete Verification:** Not checking if changes actually solve the stated problem
- **Missing Context:** Not understanding the full system before making changes
- **Reverting Issues:** Making changes that require later rollbacks due to unintended consequences

---

*Last Updated: 2025-01-20*
