import{a as f,j as e}from"../assets/index-Bb6RUuxY.js";import{r as t}from"./vendor-C67cHu0f.js";function w({children:r,className:i=""}){const{theme:o}=f(),[c,a]=t.useState(!0),[l,d]=t.useState(!1),n=t.useRef(null);t.useEffect(()=>{const s=()=>{if(n.current){const{scrollWidth:u,clientWidth:h}=n.current;d(u>h)}};return s(),window.addEventListener("resize",s),()=>window.removeEventListener("resize",s)},[r]),t.useEffect(()=>{if(!l){a(!1);return}const s=setTimeout(()=>{a(!1)},5e3);return()=>clearTimeout(s)},[l]);const m=()=>{a(!1)};return e.jsxs("div",{className:`relative ${i}`,children:[c&&l&&e.jsx("div",{className:"md:hidden absolute top-2 right-2 z-20 pointer-events-none",children:e.jsxs("div",{className:`flex items-center gap-2 ${o.primary} text-white px-3 py-2 rounded-full shadow-lg backdrop-blur-sm animate-bounce`,children:[e.jsx("span",{className:"text-xs font-medium",children:"Swipe to see more"}),e.jsxs("div",{className:"flex space-x-1",children:[e.jsx("div",{className:"w-1 h-1 bg-white rounded-full animate-pulse"}),e.jsx("div",{className:"w-1 h-1 bg-white rounded-full animate-pulse",style:{animationDelay:"0.2s"}}),e.jsx("div",{className:"w-1 h-1 bg-white rounded-full animate-pulse",style:{animationDelay:"0.4s"}})]}),e.jsx("span",{className:"text-sm",children:"→"})]})}),l&&e.jsx("div",{className:`md:hidden absolute top-0 right-0 bottom-0 w-8 bg-gradient-to-l ${o.card}/80 to-transparent z-10 pointer-events-none`}),e.jsx("div",{ref:n,className:"overflow-x-auto overflow-y-hidden",style:{WebkitOverflowScrolling:"touch"},onScroll:m,children:r})]})}export{w as S};
