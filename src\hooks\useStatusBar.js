import { useEffect } from 'react';
import { StatusBar } from '@capacitor/status-bar';
import { NavigationBar } from '@capgo/capacitor-navigation-bar';
import { Capacitor } from '@capacitor/core';
import { useTheme } from '../context/ThemeContext';

export function useStatusBar() {
  const { currentTheme } = useTheme();

  useEffect(() => {
    // Only run on mobile platforms
    if (!Capacitor.isNativePlatform()) {
      return;
    }

    const updateStatusBar = async () => {
      try {
        // Small delay to ensure native platform is ready
        await new Promise(resolve => setTimeout(resolve, 100));

        // Force status bar to not overlay content - this should create proper spacing
        await StatusBar.setOverlaysWebView({ overlay: false });

        // Show the status bar first to ensure it's visible
        await StatusBar.show();

        // Ensure navigation bar is visible and not hidden
        try {
          await NavigationBar.show();
        } catch (navShowError) {
          console.log('NavigationBar.show() not available:', navShowError);
        }

        // Define 85% intensity theme colors for status bar and navigation bar (85% of header shades)
        const systemBarColors = {
          'dark': '#4B5563',     // gray-600 (85% of app header gray-900 #111827)
          'electric': '#3B82F6', // blue-500 (85% of app header blue-550 #1a56db)
          'green': '#10B981',    // green-500 (85% of app header green-550 #0e9f6e)
          'teal': '#14B8A6',     // teal-500 (85% of app header teal-550 #0d9488)
          'pink': '#EC4899'      // pink-500 (85% of app header pink-550 #db2777)
        };

        // Get the 85% intensity theme color for both status bar and navigation bar
        const systemBarColor = systemBarColors[currentTheme] || '#3B82F6';

        // Configure status bar with consistent white content visibility
        // LIGHT style = white text/icons, DARK style = black text/icons
        // Always use LIGHT style for white text/icons across all themes
        const statusBarStyle = 'LIGHT';

        // Set style first, then background color
        await StatusBar.setStyle({ style: statusBarStyle });

        // Force the background color with explicit hex format
        await StatusBar.setBackgroundColor({
          color: systemBarColor.toUpperCase() // Ensure uppercase hex format
        });

        // Configure navigation bar to use the same 85% intensity color as status bar
        // Use the correct method name: setNavigationBarColor (not setColor)
        await NavigationBar.setNavigationBarColor({
          color: systemBarColor.toUpperCase(), // Ensure uppercase hex format
          darkButtons: false // Light buttons (white) to match white status bar content
        });

      } catch (error) {
        console.log('StatusBar or NavigationBar plugin not available:', error);
      }
    };

    updateStatusBar();
  }, [currentTheme]);
}
