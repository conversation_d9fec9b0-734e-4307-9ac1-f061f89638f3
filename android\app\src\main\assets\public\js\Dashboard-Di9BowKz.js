import{u as v,a as S,j as e}from"../assets/index-Bb6RUuxY.js";import{r as N,b as C}from"./vendor-C67cHu0f.js";import{D as T,C as U,A as D,p as F,a as A}from"./charts-B70j_Dbf.js";import{H as b,m as j,d as E,n as H,o as L,l as $}from"./icons-Bhz3yUky.js";import{N as G}from"./NativeAd-CG0EkLvY.js";import{S as W}from"./SwipeableLayout-CndH0mui.js";import"./utils-CgIdLkdF.js";U.register(D,F,A);function B(){const{state:a,usageSinceLastRecording:i,getDisplayUnitName:r}=v(),{theme:s}=S(),t=N.useRef(null),c=a.currentUnits,o=i,d=c+o,x=d>0?o/d*100:0,p=()=>{const l=t.current;if(l&&l.ctx&&l.canvas){const f=l.ctx,h=l.canvas,n=h.width/2,m=h.height/2,w=Math.min(n,m)*.2,y=Math.min(n,m)*.8,u=f.createRadialGradient(n,m,w,n,m,y);u.addColorStop(0,"#667eea"),u.addColorStop(.3,"#764ba2"),u.addColorStop(.6,"#667eea"),u.addColorStop(1,"#f093fb");const g=f.createRadialGradient(n,m,w,n,m,y);g.addColorStop(0,"#ff9a9e"),g.addColorStop(.3,"#fecfef"),g.addColorStop(.6,"#fecfef"),g.addColorStop(1,"#ffc3a0"),l.data.datasets[0].backgroundColor=[u,g],l.update()}};N.useEffect(()=>{p()},[c,o]),N.useEffect(()=>{const l=setTimeout(()=>{p()},100);return()=>clearTimeout(l)},[]);const k={labels:[`Remaining ${r()}`,`Used ${r()}`],datasets:[{data:[c,o],backgroundColor:["linear-gradient(135deg, #667eea 0%, #764ba2 100%)","linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #ffc3a0 100%)"],borderColor:["rgba(255, 255, 255, 0.9)","rgba(255, 255, 255, 0.9)"],borderWidth:4,cutout:"78%",borderRadius:12,borderJoinStyle:"round",hoverBorderWidth:6,hoverBorderColor:["rgba(255, 255, 255, 1)","rgba(255, 255, 255, 1)"],shadowOffsetX:3,shadowOffsetY:3,shadowBlur:10,shadowColor:"rgba(0, 0, 0, 0.1)"}]},R={responsive:!0,maintainAspectRatio:!1,onResize:()=>{setTimeout(()=>p(),50)},plugins:{legend:{display:!1},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"#ffffff",bodyColor:"#ffffff",borderColor:"rgba(255, 255, 255, 0.2)",borderWidth:1,cornerRadius:8,displayColors:!0,callbacks:{label:function(l){const f=l.label||"",h=l.parsed,n=d>0?(h/d*100).toFixed(1):0;return`${f}: ${h.toFixed(2)} (${n}%)`}}}},animation:{animateRotate:!0,animateScale:!0,duration:2e3,easing:"easeInOutCubic",delay:l=>l.dataIndex*200,onComplete:()=>{setTimeout(()=>p(),100)}},interaction:{intersect:!1,mode:"nearest"},elements:{arc:{borderWidth:4,hoverBorderWidth:6,borderSkipped:!1,borderAlign:"inner"}},layout:{padding:{top:10,bottom:10,left:10,right:10}}};return e.jsxs("div",{className:"relative",children:[e.jsxs("div",{className:"relative h-[16rem] md:h-[20rem] lg:h-[24rem] mb-4 md:mb-6 flex items-center justify-center",children:[e.jsx("div",{className:`absolute top-3 md:top-6 left-3 md:left-6 w-8 md:w-12 h-8 md:h-12 bg-gradient-to-r ${s.gradient} rounded-full opacity-20 blur-xl animate-pulse`}),e.jsx("div",{className:`absolute bottom-3 md:bottom-6 right-3 md:right-6 w-10 md:w-16 h-10 md:h-16 bg-gradient-to-r ${s.gradient} rounded-full opacity-15 blur-2xl animate-pulse`,style:{animationDelay:"1s"}}),e.jsx("div",{className:`absolute top-1/2 left-2 md:left-4 w-10 md:w-16 h-10 md:h-16 bg-gradient-to-r ${s.gradient} rounded-full opacity-10 blur-lg animate-pulse`,style:{animationDelay:"2s"}}),e.jsx("div",{className:`absolute top-1/4 right-4 md:right-8 w-8 md:w-12 h-8 md:h-12 bg-gradient-to-r ${s.gradient} rounded-full opacity-25 blur-md animate-pulse`,style:{animationDelay:"0.5s"}}),e.jsxs("div",{className:"relative h-full w-full max-w-xs md:max-w-md lg:max-w-lg flex items-center justify-center p-4 md:p-6",children:[e.jsx(T,{ref:t,data:k,options:R}),e.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-0 w-40 h-40 md:w-56 lg:w-64 md:h-56 lg:h-64 bg-gradient-to-r from-indigo-400 via-purple-500 to-pink-500 rounded-full blur-2xl opacity-40 animate-pulse"}),e.jsxs("div",{className:`relative w-40 h-40 md:w-56 lg:w-64 md:h-56 lg:h-64 ${s.card}/50 border-2 md:border-4 ${s.border}/40 backdrop-blur-xl rounded-full shadow-2xl flex items-center justify-center`,children:[e.jsx("div",{className:`absolute inset-2 md:inset-3 ${s.secondary}/70 rounded-full`}),e.jsxs("div",{className:"relative text-center z-10",children:[e.jsx("div",{className:"mb-1 flex justify-center",children:e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full blur-sm opacity-75 animate-pulse"}),e.jsx(b,{className:"relative h-4 md:h-6 lg:h-8 w-4 md:w-6 lg:w-8 text-transparent bg-gradient-to-r from-yellow-500 to-orange-600 bg-clip-text animate-bounce",style:{animationDuration:"2s"}})]})}),e.jsxs("div",{className:"relative mb-1",children:[e.jsx("div",{className:`text-xl md:text-3xl lg:text-4xl font-black ${s.text} drop-shadow-lg`,children:c.toFixed(2)}),e.jsxs("div",{className:`text-xs md:text-sm lg:text-base font-bold ${s.textSecondary} mt-0.5 tracking-wide`,children:[r()," Left"]})]}),e.jsx("div",{className:"mt-0.5",children:e.jsxs("div",{className:`text-xs md:text-sm lg:text-base font-bold tracking-tight ${s.textSecondary} drop-shadow-lg`,children:[x.toFixed(1),"% Used"]})})]}),e.jsx("div",{className:"absolute top-6 right-6 w-3 h-3 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full opacity-70 animate-pulse"}),e.jsx("div",{className:"absolute bottom-6 left-6 w-2 h-2 bg-gradient-to-r from-pink-400 to-orange-400 rounded-full opacity-60 animate-pulse",style:{animationDelay:"1s"}}),e.jsx("div",{className:"absolute top-1/4 right-8 w-1.5 h-1.5 bg-gradient-to-r from-green-400 to-blue-400 rounded-full opacity-50 animate-pulse",style:{animationDelay:"1.5s"}}),e.jsx("div",{className:"absolute bottom-1/4 left-8 w-1 h-1 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full opacity-40 animate-pulse",style:{animationDelay:"0.5s"}})]})]})})]})]}),e.jsxs("div",{className:"mt-4 space-y-3",children:[e.jsxs("div",{className:`relative overflow-hidden rounded-2xl ${s.card} border ${s.border} p-3 md:p-4 shadow-lg mobile-card-auto`,children:[e.jsx("div",{className:`absolute inset-0 ${s.secondary}`}),e.jsx("div",{className:"relative",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:`text-xs font-semibold ${s.textSecondary} tracking-wider uppercase mb-1`,children:"CURRENT UNITS"}),e.jsx("p",{className:`text-lg md:text-xl font-bold ${s.text} mb-1`,children:a.currentUnits.toFixed(2)}),e.jsxs("p",{className:`text-xs ${s.textSecondary}`,children:[r()," remaining"]}),e.jsxs("p",{className:`text-xs ${s.textSecondary}`,children:["Value: ",a.currencySymbol,(a.currentUnits*a.unitCost).toFixed(2)]})]}),e.jsx("div",{className:`p-2 md:p-3 rounded-xl bg-gradient-to-br ${s.gradient} shadow-lg ml-3`,children:e.jsx(b,{className:"h-5 md:h-6 w-5 md:w-6 text-white"})})]})}),e.jsx("div",{className:`absolute top-2 right-2 w-3 h-3 bg-gradient-to-r ${s.gradient} rounded-full opacity-15 blur-sm`}),e.jsx("div",{className:`absolute bottom-2 left-2 w-4 h-4 bg-gradient-to-r ${s.gradient} rounded-full opacity-20 blur-sm`})]}),a.usageHistory.length>0&&e.jsxs("div",{className:`relative overflow-hidden rounded-2xl ${s.card} border ${s.border} p-3 md:p-4 shadow-lg mobile-card-auto`,children:[e.jsx("div",{className:`absolute inset-0 ${s.secondary}`}),e.jsx("div",{className:"relative",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:`text-xs font-semibold ${s.textSecondary} tracking-wider uppercase mb-1`,children:"USAGE SINCE LAST RECORDING"}),e.jsx("p",{className:`text-lg md:text-xl font-bold ${s.text} mb-1`,children:i.toFixed(2)}),e.jsxs("p",{className:`text-xs ${s.textSecondary}`,children:[r()," used"]}),e.jsxs("p",{className:`text-xs ${s.textSecondary}`,children:["Cost: ",a.currencySymbol,(i*a.unitCost).toFixed(2)]})]}),e.jsx("div",{className:`p-2 md:p-3 rounded-xl bg-gradient-to-br ${s.gradient} shadow-lg ml-3`,children:e.jsx(j,{className:"h-5 md:h-6 w-5 md:w-6 text-white"})})]})}),e.jsx("div",{className:`absolute top-2 right-2 w-4 h-4 bg-gradient-to-r ${s.gradient} rounded-full opacity-20 blur-sm`}),e.jsx("div",{className:`absolute bottom-2 left-2 w-3 h-3 bg-gradient-to-r ${s.gradient} rounded-full opacity-15 blur-sm`})]})]}),e.jsxs("div",{className:"mt-3 space-y-3",children:[a.usageHistory.length>0&&e.jsxs("div",{className:`relative overflow-hidden rounded-2xl ${s.card} border ${s.border} p-3 md:p-4 shadow-lg mobile-card-auto`,children:[e.jsx("div",{className:`absolute inset-0 ${s.secondary}`}),e.jsx("div",{className:"relative",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:`text-xs font-semibold ${s.textSecondary} tracking-wider uppercase mb-1`,children:"TOTAL COST"}),e.jsxs("div",{className:`text-lg md:text-xl font-black ${s.text} mb-1`,children:[a.currencySymbol||"R",(o*a.unitCost).toFixed(2)]}),e.jsxs("div",{className:`text-xs ${s.textSecondary}`,children:["For ",o.toFixed(2)," ",r()," used"]})]}),e.jsx("div",{className:`p-2 md:p-3 rounded-xl bg-gradient-to-br ${s.gradient} shadow-lg ml-3`,children:e.jsx(E,{className:"h-5 md:h-6 w-5 md:w-6 text-white"})})]})}),e.jsx("div",{className:`absolute top-2 right-2 w-4 h-4 bg-gradient-to-r ${s.gradient} rounded-full opacity-20 blur-sm`}),e.jsx("div",{className:`absolute bottom-2 left-2 w-3 h-3 bg-gradient-to-r ${s.gradient} rounded-full opacity-15 blur-sm`})]}),e.jsxs("div",{className:`relative overflow-hidden rounded-2xl ${s.card} border ${s.border} p-3 md:p-4 shadow-lg mobile-card-auto`,children:[e.jsx("div",{className:`absolute inset-0 ${s.secondary}`}),e.jsx("div",{className:"relative",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:`text-xs font-semibold ${s.textSecondary} tracking-wider uppercase mb-1`,children:"CURRENT RATE"}),e.jsxs("div",{className:`text-lg md:text-xl font-black ${s.text} mb-1`,children:[a.currencySymbol||"R",a.unitCost.toFixed(2)]}),e.jsxs("div",{className:`text-xs ${s.textSecondary}`,children:["Per ",r()]})]}),e.jsx("div",{className:`p-2 md:p-3 rounded-xl bg-gradient-to-br ${s.gradient} shadow-lg ml-3`,children:e.jsx(H,{className:"h-5 md:h-6 w-5 md:w-6 text-white"})})]})}),e.jsx("div",{className:`absolute top-2 right-2 w-3 h-3 bg-gradient-to-r ${s.gradient} rounded-full opacity-15 blur-sm`}),e.jsx("div",{className:`absolute bottom-2 left-2 w-4 h-4 bg-gradient-to-r ${s.gradient} rounded-full opacity-20 blur-sm`})]})]}),e.jsxs("div",{className:"mt-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-3",children:[e.jsx("span",{className:`text-xs font-bold ${s.textSecondary} tracking-wide uppercase`,children:"Usage Progress"}),e.jsxs("span",{className:`text-sm font-black px-2 py-1 rounded-full ${s.secondary} ${s.text}`,children:[x.toFixed(1),"%"]})]}),e.jsx("div",{className:"relative",children:e.jsx("div",{className:`w-full ${s.secondary} rounded-full h-4 border ${s.border}`,children:e.jsx("div",{className:`h-4 rounded-full transition-all duration-1000 ease-out bg-gradient-to-r ${x>70?"from-red-500 to-red-600":x>40?"from-amber-500 to-orange-600":"from-emerald-500 to-green-600"}`,style:{width:`${Math.min(x,100)}%`}})})})]})]})}function O(){const a=C(),{state:i,getDisplayUnitName:r}=v(),s=i.currentUnits,t=i.thresholdLimit;return e.jsx("div",{className:"bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-xl p-6 shadow-lg",children:e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("div",{className:"p-2 bg-gradient-to-r from-amber-400 to-orange-500 rounded-lg",children:e.jsx(L,{className:"h-6 w-6 text-white animate-pulse"})})}),e.jsxs("div",{className:"ml-4 flex-1",children:[e.jsxs("h3",{className:"text-xl font-bold text-amber-800 mb-2",children:["⚠️ Low ",r()," Warning!"]}),e.jsxs("div",{className:"text-sm text-amber-700 space-y-2",children:[e.jsxs("p",{className:"font-medium",children:["You have ",e.jsxs("strong",{className:"text-amber-900",children:[s.toFixed(2)," ",r()]})," remaining, which is below your threshold of ",e.jsxs("strong",{className:"text-amber-900",children:[t.toFixed(2)," ",r()]}),"."]}),e.jsxs("p",{children:["💡 ",e.jsx("strong",{children:"Time to top up!"})," Consider purchasing more ",r()," to avoid running out of power."]})]}),e.jsxs("div",{className:"mt-4 flex flex-wrap gap-3",children:[e.jsx("button",{onClick:()=>a("/purchases"),className:"bg-gradient-to-r from-emerald-500 to-green-600 text-white px-6 py-3 rounded-lg text-sm font-semibold hover:from-emerald-600 hover:to-green-700 transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105",children:"🛒 Top Up Now"}),e.jsx("button",{onClick:()=>a("/settings"),className:"bg-white text-amber-700 border-2 border-amber-300 px-6 py-3 rounded-lg text-sm font-semibold hover:bg-amber-50 hover:border-amber-400 transition-all duration-200",children:"⚙️ Adjust Threshold"})]})]})]})})}function _(){const a=C(),{state:i,isThresholdExceeded:r,getDisplayUnitName:s}=v(),{theme:t}=S(),c=e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:`${t.card} rounded-2xl shadow-lg p-4 md:p-6 lg:p-8 border ${t.border} w-full`,children:e.jsx(B,{})}),e.jsx(G,{}),e.jsxs("div",{className:`${t.card} rounded-2xl shadow-lg p-6 lg:p-8 border ${t.border}`,children:[e.jsxs("h2",{className:`text-xl lg:text-2xl font-bold ${t.text} mb-6 flex items-center gap-3`,children:[e.jsx("div",{className:`p-3 lg:p-4 rounded-xl bg-gradient-to-br ${t.gradient} shadow-lg`,children:e.jsx(b,{className:"h-6 lg:h-7 w-6 lg:w-7 text-white"})}),"Quick Actions"]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("button",{onClick:()=>a("/purchases"),className:`w-full flex items-center gap-3 p-3 ${t.primary} text-white rounded-xl hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`,children:[e.jsx($,{className:"h-5 w-5"}),e.jsxs("div",{className:"text-left",children:[e.jsx("span",{className:"block text-sm font-semibold",children:"Add Purchase"}),e.jsx("span",{className:"block text-xs opacity-80",children:"Top up your units"})]})]}),e.jsxs("button",{onClick:()=>a("/usage"),className:`w-full flex items-center gap-3 p-3 ${t.primary} text-white rounded-xl hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`,children:[e.jsx(j,{className:"h-5 w-5"}),e.jsxs("div",{className:"text-left",children:[e.jsx("span",{className:"block text-sm font-semibold",children:"Record Usage"}),e.jsx("span",{className:"block text-xs opacity-80",children:"Track consumption"})]})]}),e.jsxs("button",{onClick:()=>a("/history"),className:`w-full flex items-center gap-3 p-3 ${t.primary} text-white rounded-xl hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`,children:[e.jsx(b,{className:"h-5 w-5"}),e.jsxs("div",{className:"text-left",children:[e.jsx("span",{className:"block text-sm font-semibold",children:"View History"}),e.jsx("span",{className:"block text-xs opacity-80",children:"See all records"})]})]})]})]})]}),o=e.jsxs("div",{className:`${t.card} rounded-2xl shadow-lg p-6 lg:p-8 border ${t.border} flex flex-col`,children:[e.jsxs("h2",{className:`text-xl lg:text-2xl font-bold ${t.text} mb-6 flex items-center gap-3`,children:[e.jsx("div",{className:`p-3 lg:p-4 rounded-xl bg-gradient-to-br ${t.gradient} shadow-lg`,children:e.jsx(j,{className:"h-6 lg:h-7 w-6 lg:w-7 text-white"})}),"Recent Activity"]}),e.jsxs("div",{className:"space-y-3",children:[i.purchases.slice(0,2).map(d=>e.jsx("div",{className:`p-3 ${t.secondary} border ${t.border} rounded-xl shadow-sm hover:shadow-md transition-all duration-200`,children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`p-2 rounded-lg ${t.accent} shadow-sm`,children:e.jsx($,{className:"h-4 w-4 text-white"})}),e.jsxs("div",{className:"ml-3",children:[e.jsxs("p",{className:`text-sm font-semibold ${t.text}`,children:["Purchase: ",i.currencySymbol||"R",d.currency.toFixed(2)]}),e.jsx("p",{className:`text-xs ${t.textSecondary}`,children:new Date(d.date).toLocaleDateString()})]})]}),e.jsxs("span",{className:`text-sm font-semibold ${t.text}`,children:["+",d.units.toFixed(2)," ",s()]})]})},d.id)),i.usageHistory.slice(0,1).map(d=>e.jsx("div",{className:`p-3 ${t.secondary} border ${t.border} rounded-xl shadow-sm hover:shadow-md transition-all duration-200`,children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`p-2 rounded-lg ${t.accent} shadow-sm`,children:e.jsx(j,{className:"h-4 w-4 text-white"})}),e.jsxs("div",{className:"ml-3",children:[e.jsx("p",{className:`text-sm font-semibold ${t.text}`,children:"Usage recorded"}),e.jsx("p",{className:`text-xs ${t.textSecondary}`,children:new Date(d.date).toLocaleDateString()})]})]}),e.jsxs("span",{className:`text-sm font-semibold ${t.text}`,children:["-",d.usage.toFixed(2)," ",s()]})]})},d.id)),i.purchases.length===0&&i.usageHistory.length===0&&e.jsxs("div",{className:`text-center py-8 ${t.secondary} border ${t.border} rounded-xl`,children:[e.jsx("div",{className:`p-3 rounded-2xl bg-gradient-to-br ${t.gradient} w-fit mx-auto mb-3`,children:e.jsx(b,{className:"h-8 w-8 text-white"})}),e.jsx("p",{className:`text-sm ${t.textSecondary} font-medium`,children:"No recent activity"}),e.jsx("p",{className:`text-xs ${t.textSecondary} mt-1`,children:"Start by making a purchase or recording usage"})]})]})]});return e.jsxs("div",{className:"w-full space-y-6 pb-6",children:[r&&e.jsx(O,{}),e.jsx(W,{leftContent:c,rightContent:o,rightContentTitle:"Recent Activity",className:""})]})}export{_ as default};
